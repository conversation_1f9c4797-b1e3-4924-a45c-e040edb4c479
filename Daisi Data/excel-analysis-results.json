{"Export Summary": {"rowCount": 7, "columns": ["This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel."], "qualityAnalysis": {"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": {"fillRate": "57.1%", "uniqueValues": 4, "dataTypes": ["string"], "sampleValues": ["Numbers Sheet Name", "FOD centres", "Care+Dr"]}}, "sampleData": [{"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": "Numbers Sheet Name"}, {"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": "FOD centres"}, {"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": null}, {"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": "Care+Dr"}, {"This document was exported from Numbers. Each table was converted to an Excel worksheet. All other objects on each Numbers sheet were placed on separate worksheets. Please be aware that formula calculations may differ in Excel.": null}]}, "FOD centres": {"rowCount": 72, "columns": ["Organisation", "First Name", "Last Name", "E-mail Address", "Phone", "Mobile Phone", "Address 1", "Address 2", "Postal Code", "Website", "Emailed", "Signed up"], "qualityAnalysis": {"Organisation": {"fillRate": "100.0%", "uniqueValues": 72, "dataTypes": ["string"], "sampleValues": ["Alderton Village Hall", "Alvington Memorial Hall", "<PERSON>"]}, "First Name": {"fillRate": "59.7%", "uniqueValues": 36, "dataTypes": ["string"], "sampleValues": ["<PERSON>", "<PERSON>", "<PERSON>"]}, "Last Name": {"fillRate": "55.6%", "uniqueValues": 37, "dataTypes": ["string"], "sampleValues": ["<PERSON>", "<PERSON>", "Cross"]}, "E-mail Address": {"fillRate": "88.9%", "uniqueValues": 63, "dataTypes": ["email"], "sampleValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "Phone": {"fillRate": "55.6%", "uniqueValues": 40, "dataTypes": ["phone", "integer", "email"], "sampleValues": ["01594 529287", "01594 544094", "01531 820132"]}, "Mobile Phone": {"fillRate": "31.9%", "uniqueValues": 22, "dataTypes": ["phone", "email", "string"], "sampleValues": ["07748 456452", "07958 590508", "07771 902425"]}, "Address 1": {"fillRate": "84.7%", "uniqueValues": 57, "dataTypes": ["string"], "sampleValues": ["2 Dibden Lane", "Knapp Lane", "Ash Dene Rd,"]}, "Address 2": {"fillRate": "72.2%", "uniqueValues": 38, "dataTypes": ["string"], "sampleValues": ["Tewkesbury", "<PERSON><PERSON><PERSON>", "Awre"]}, "Postal Code": {"fillRate": "81.9%", "uniqueValues": 58, "dataTypes": ["postcode", "string"], "sampleValues": ["GL20 8NT", "GL15 6BQ", "GL17 9UQ"]}, "Website": {"fillRate": "69.4%", "uniqueValues": 50, "dataTypes": ["url", "string"], "sampleValues": ["aldertonvillagehall.co.uk", "facebook.com/AlvingtonMemorialHall", "facebook.com/pluddsvillagehall"]}, "Emailed": {"fillRate": "0.0%", "uniqueValues": 0, "dataTypes": [], "sampleValues": []}, "Signed up": {"fillRate": "0.0%", "uniqueValues": 0, "dataTypes": [], "sampleValues": []}}, "sampleData": [{"Organisation": "Alderton Village Hall", "First Name": null, "Last Name": null, "E-mail Address": "<EMAIL>", "Phone": null, "Mobile Phone": null, "Address 1": "2 Dibden Lane", "Address 2": "Tewkesbury", "Postal Code": "GL20 8NT", "Website": "aldertonvillagehall.co.uk", "Emailed": null, "Signed up": null}, {"Organisation": "Alvington Memorial Hall", "First Name": null, "Last Name": null, "E-mail Address": "<EMAIL>", "Phone": "01594 529287", "Mobile Phone": null, "Address 1": "Knapp Lane", "Address 2": "<PERSON><PERSON><PERSON>", "Postal Code": "GL15 6BQ", "Website": "facebook.com/AlvingtonMemorialHall", "Emailed": null, "Signed up": null}, {"Organisation": "<PERSON>", "First Name": "<PERSON>", "Last Name": "<PERSON>", "E-mail Address": "<EMAIL>", "Phone": "01594 544094", "Mobile Phone": null, "Address 1": "Ash Dene Rd,", "Address 2": null, "Postal Code": "GL17 9UQ", "Website": "facebook.com/pluddsvillagehall", "Emailed": null, "Signed up": null}, {"Organisation": "<PERSON>", "First Name": "<PERSON>", "Last Name": "<PERSON>", "E-mail Address": "<EMAIL>", "Phone": "01531 820132", "Mobile Phone": null, "Address 1": "Gloucester Road", "Address 2": null, "Postal Code": "GL18 1EJ", "Website": "upleadon-village.co.uk/village-hall/", "Emailed": null, "Signed up": null}, {"Organisation": "Awre Village Hall", "First Name": "<PERSON>", "Last Name": "Cross", "E-mail Address": null, "Phone": "01594 517127", "Mobile Phone": null, "Address 1": "Northington Lane", "Address 2": "Awre", "Postal Code": null, "Website": null, "Emailed": null, "Signed up": null}]}, "Care+Dr": {"rowCount": 100, "columns": ["premises", "name", "address", "left with:", "date:", "Comments"], "qualityAnalysis": {"premises": {"fillRate": "75.0%", "uniqueValues": 44, "dataTypes": ["string"], "sampleValues": ["Coleford", "GP Surgery", "GP surgery"]}, "name": {"fillRate": "75.0%", "uniqueValues": 72, "dataTypes": ["string"], "sampleValues": ["Coleford Health Centre", "Brunston Surgery", "Library"]}, "address": {"fillRate": "75.0%", "uniqueValues": 69, "dataTypes": ["string"], "sampleValues": ["Railway Drive, Coleford", "Cinder Hill Coleford", "Main Place Coleford"]}, "left with:": {"fillRate": "73.0%", "uniqueValues": 69, "dataTypes": ["string"], "sampleValues": ["<PERSON>", "Library Staff", "<PERSON><PERSON>"]}, "date:": {"fillRate": "71.0%", "uniqueValues": 14, "dataTypes": ["date"], "sampleValues": [45734, 45745, 45735]}, "Comments": {"fillRate": "27.0%", "uniqueValues": 26, "dataTypes": ["string", "email"], "sampleValues": ["very enthusiastic would like lots more", "very keen", "very enthusiastic"]}}, "sampleData": [{"premises": "Coleford", "name": null, "address": null, "left with:": null, "date:": null, "Comments": null}, {"premises": "GP Surgery", "name": "Coleford Health Centre", "address": "Railway Drive, Coleford", "left with:": "<PERSON>", "date:": 45734, "Comments": null}, {"premises": "GP surgery", "name": "Brunston Surgery", "address": "Cinder Hill Coleford", "left with:": "<PERSON>", "date:": 45734, "Comments": null}, {"premises": "Library", "name": "Library", "address": "Main Place Coleford", "left with:": "Library Staff", "date:": 45734, "Comments": null}, {"premises": "Care ", "name": "Step a side", "address": "29 Market Place Coleford", "left with:": null, "date:": null, "Comments": null}]}, "Active pursuits": {"rowCount": 28, "columns": ["Organisation", "Name", "Email", "Phone", "Mobile", "Address", "Action 1", "Action 2"], "qualityAnalysis": {"Organisation": {"fillRate": "35.7%", "uniqueValues": 8, "dataTypes": ["string"], "sampleValues": ["Lydbrook Baptist Church", "Ruspidge Memorial Hall", "Computer Club Lydney Community Centre"]}, "Name": {"fillRate": "64.3%", "uniqueValues": 18, "dataTypes": ["string"], "sampleValues": ["<PERSON>", "<PERSON> ", "<PERSON>"]}, "Email": {"fillRate": "42.9%", "uniqueValues": 12, "dataTypes": ["email"], "sampleValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "Phone": {"fillRate": "25.0%", "uniqueValues": 7, "dataTypes": ["phone", "email"], "sampleValues": ["01594 833251", "01594 781449", "01594 827711"]}, "Mobile": {"fillRate": "7.1%", "uniqueValues": 2, "dataTypes": ["phone"], "sampleValues": ["07504 565153", "07522 178639"]}, "Address": {"fillRate": "67.9%", "uniqueValues": 19, "dataTypes": ["string", "postcode"], "sampleValues": ["Lower Lydbrook", "Ruspidge Road Ruspidge Cinderford GL14 3AE", "<PERSON><PERSON>"]}, "Action 1": {"fillRate": "28.6%", "uniqueValues": 8, "dataTypes": ["string"], "sampleValues": ["Good meeting, <PERSON> I think will assist", "They need help publising the computer club and DAISI could come in on top of their basic actvities.", "Sent in proposal waiting on response from Charlotte"]}, "Action 2": {"fillRate": "28.6%", "uniqueValues": 8, "dataTypes": ["string"], "sampleValues": ["Need to organise first meet and see what they want", "Meeting David on Thursday 10th", "Meeting <PERSON> and <PERSON> to sort some email issues."]}}, "sampleData": [{"Organisation": "Lydbrook Baptist Church", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": "01594 833251", "Mobile": null, "Address": "Lower Lydbrook", "Action 1": "Good meeting, <PERSON> I think will assist", "Action 2": "Need to organise first meet and see what they want"}, {"Organisation": "Lydbrook Baptist Church", "Name": "<PERSON> ", "Email": "<EMAIL>", "Phone": null, "Mobile": null, "Address": null, "Action 1": null, "Action 2": null}, {"Organisation": "Lydbrook Baptist Church", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": null, "Mobile": null, "Address": null, "Action 1": null, "Action 2": "Meeting David on Thursday 10th"}, {"Organisation": "Ruspidge Memorial Hall", "Name": "<PERSON>", "Email": "<EMAIL>", "Phone": null, "Mobile": null, "Address": "Ruspidge Road Ruspidge Cinderford GL14 3AE", "Action 1": null, "Action 2": "Meeting <PERSON> and <PERSON> to sort some email issues."}, {"Organisation": null, "Name": null, "Email": "<EMAIL>", "Phone": null, "Mobile": null, "Address": null, "Action 1": null, "Action 2": "Waiting for group what do we want to do."}]}}