// Test script for Active Pursuits Convex functions
// This script validates the function structure and data format

const fs = require('fs');

// Test data sample
const testRecords = [
  {
    organisation: "Test Baptist Church",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "01594 123456",
    mobile: "07700 123456",
    address: "123 Test Street, Test Town",
    action_1: "Initial contact made",
    action_2: "Follow-up meeting scheduled"
  },
  {
    organisation: "Test Community Centre",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: null,
    mobile: "07700 654321",
    address: "456 Community Road",
    action_1: "Proposal sent",
    action_2: null
  }
];

// Validate function structure
function validateFunctionStructure() {
  console.log('🔍 Validating function files...');
  
  const files = [
    '../Daisi/convex/functions/activepursuits/insertBatch.js',
    '../Daisi/convex/functions/activepursuits/queries.js',
    '../Daisi/convex/functions/activepursuits/mutations.js',
    '../Daisi/convex/functions/activepursuits/index.js'
  ];
  
  const results = [];
  
  files.forEach(file => {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const hasImports = content.includes('import');
        const hasExports = content.includes('export');
        const hasConvexTypes = content.includes('v.');
        
        results.push({
          file: file.split('/').pop(),
          exists: true,
          hasImports,
          hasExports,
          hasConvexTypes,
          size: content.length
        });
      } else {
        results.push({
          file: file.split('/').pop(),
          exists: false
        });
      }
    } catch (error) {
      results.push({
        file: file.split('/').pop(),
        exists: false,
        error: error.message
      });
    }
  });
  
  return results;
}

// Validate data format
function validateDataFormat(records) {
  console.log('📊 Validating data format...');
  
  const validationResults = {
    totalRecords: records.length,
    validRecords: 0,
    issues: []
  };
  
  records.forEach((record, index) => {
    const recordIssues = [];
    
    // Check required fields
    if (!record.organisation && !record.name) {
      recordIssues.push('Missing both organisation and name');
    }
    
    // Check email format
    if (record.email && !record.email.includes('@')) {
      recordIssues.push('Invalid email format');
    }
    
    // Check phone format (basic check)
    if (record.phone && !/^\+?[\d\s\-\(\)]+$/.test(record.phone)) {
      recordIssues.push('Invalid phone format');
    }
    
    if (recordIssues.length === 0) {
      validationResults.validRecords++;
    } else {
      validationResults.issues.push({
        recordIndex: index,
        record: record,
        issues: recordIssues
      });
    }
  });
  
  return validationResults;
}

// Main test function
function runTests() {
  console.log('🧪 Testing Active Pursuits Convex Functions');
  console.log('='.repeat(50));
  
  // Test 1: Function structure validation
  console.log('\n📁 Function Structure Validation:');
  const structureResults = validateFunctionStructure();
  console.table(structureResults);
  
  // Test 2: Data format validation
  console.log('\n📊 Data Format Validation:');
  const dataResults = validateDataFormat(testRecords);
  console.log(`✅ Valid records: ${dataResults.validRecords}/${dataResults.totalRecords}`);
  
  if (dataResults.issues.length > 0) {
    console.log('\n⚠️  Data Issues Found:');
    dataResults.issues.forEach(issue => {
      console.log(`Record ${issue.recordIndex}: ${issue.issues.join(', ')}`);
    });
  }
  
  // Test 3: Schema compatibility
  console.log('\n🔧 Schema Compatibility:');
  try {
    const schemaFile = '../Daisi/convex/schema.ts';
    if (fs.existsSync(schemaFile)) {
      const schemaContent = fs.readFileSync(schemaFile, 'utf8');
      const hasActivePursuits = schemaContent.includes('activepursuits');
      const hasProperIndexes = schemaContent.includes('by_organisation') && 
                               schemaContent.includes('by_email') && 
                               schemaContent.includes('by_status');
      
      console.log(`✅ Schema file exists: ${fs.existsSync(schemaFile)}`);
      console.log(`✅ Contains activepursuits table: ${hasActivePursuits}`);
      console.log(`✅ Has proper indexes: ${hasProperIndexes}`);
    } else {
      console.log('❌ Schema file not found');
    }
  } catch (error) {
    console.log(`❌ Schema validation error: ${error.message}`);
  }
  
  console.log('\n🎯 Test Summary:');
  const allFunctionsExist = structureResults.every(r => r.exists);
  const allDataValid = dataResults.validRecords === dataResults.totalRecords;
  
  if (allFunctionsExist && allDataValid) {
    console.log('✅ All tests passed! Functions are ready to use.');
  } else {
    console.log('⚠️  Some issues found. Please review the results above.');
  }
  
  console.log('\n📖 Usage Example:');
  console.log(`
// In your Convex dashboard or API:
import { insertBatchActivePursuits } from "./functions/activepursuits/insertBatch.js";

// Insert test data:
const result = await insertBatchActivePursuits(ctx, ${JSON.stringify(testRecords.slice(0, 1), null, 2)});
console.log(result);
  `);
}

// Run tests
runTests();
