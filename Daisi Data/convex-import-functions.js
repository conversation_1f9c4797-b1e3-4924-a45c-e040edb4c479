// Generated Convex import functions
// Run these functions in your Convex dashboard or via API calls

import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Import Active Pursuits data
import { insertBatchActivePursuits } from "./functions/activepursuits/insertBatch.js";

export const importActivePursuits = mutation({
  args: {},
  handler: async (ctx) => {
    const records = [
    {
        "organisation": "Lydbrook Baptist Church",
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "01594 833251",
        "mobile": null,
        "address": "Lower Lydbrook",
        "action_1": "Good meeting, <PERSON> I think will assist",
        "action_2": "Need to organise first meet and see what they want"
    },
    {
        "organisation": "Lydbrook Baptist Church",
        "name": "<PERSON> ",
        "email": "<EMAIL>",
        "phone": null,
        "mobile": null,
        "address": null,
        "action_1": null,
        "action_2": null
    },
    {
        "organisation": "Lydbrook Baptist Church",
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": null,
        "mobile": null,
        "address": null,
        "action_1": null,
        "action_2": "Meeting David on Thursday 10th"
    },
    {
        "organisation": "Ruspidge Memorial Hall",
        "name": "Nicky",
        "email": "<EMAIL>",
        "phone": null,
        "mobile": null,
        "address": "Ruspidge Road Ruspidge Cinderford GL14 3AE",
        "action_1": null,
        "action_2": "Meeting Nick and Sally to sort some email issues."
    }
];
    
    return await insertBatchActivePursuits(ctx, records);
  },
});

// Import FOD Centres data (sample)
export const importFODCentresSample = mutation({
  args: {},
  handler: async (ctx) => {
    const records = [
    {
        "organisation": "Alderton Village Hall",
        "first_name": null,
        "last_name": null,
        "e_mail_address": "<EMAIL>",
        "phone": null,
        "mobile_phone": null,
        "address_1": "2 Dibden Lane",
        "address_2": "Tewkesbury",
        "postal_code": "GL20 8NT",
        "website": "aldertonvillagehall.co.uk",
        "emailed": null,
        "signed_up": null
    },
    {
        "organisation": "Alvington Memorial Hall",
        "first_name": null,
        "last_name": null,
        "e_mail_address": "<EMAIL>",
        "phone": "01594 529287",
        "mobile_phone": null,
        "address_1": "Knapp Lane",
        "address_2": "Alvington",
        "postal_code": "GL15 6BQ",
        "website": "facebook.com/AlvingtonMemorialHall",
        "emailed": null,
        "signed_up": null
    },
    {
        "organisation": "Ann Williams",
        "first_name": "Ann",
        "last_name": "Williams",
        "e_mail_address": "<EMAIL>",
        "phone": "01594 544094",
        "mobile_phone": null,
        "address_1": "Ash Dene Rd,",
        "address_2": null,
        "postal_code": "GL17 9UQ",
        "website": "facebook.com/pluddsvillagehall",
        "emailed": null,
        "signed_up": null
    },
    {
        "organisation": "Anne Shaw",
        "first_name": "Anne",
        "last_name": "Shaw",
        "e_mail_address": "<EMAIL>",
        "phone": "01531 820132",
        "mobile_phone": null,
        "address_1": "Gloucester Road",
        "address_2": null,
        "postal_code": "GL18 1EJ",
        "website": "upleadon-village.co.uk/village-hall/",
        "emailed": null,
        "signed_up": null
    },
    {
        "organisation": "Awre Village Hall",
        "first_name": "Ursula",
        "last_name": "Cross",
        "e_mail_address": null,
        "phone": "01594 517127",
        "mobile_phone": null,
        "address_1": "Northington Lane",
        "address_2": "Awre",
        "postal_code": null,
        "website": null,
        "emailed": null,
        "signed_up": null
    }
];
    
    // Note: This would need a corresponding insertBatch function for fodcentres table
    // Similar to insertBatchActivePursuits
    console.log("FOD Centres data ready for import:", records.length, "records");
    return { success: true, recordCount: records.length };
  },
});

// Usage Instructions:
// 1. Copy these functions to a new file in your convex/ directory
// 2. Run the import functions from your Convex dashboard
// 3. Check the data was imported correctly using the query functions

// Example API calls:
// POST /api/convex/importActivePursuits
// This will import all the Active Pursuits data from the Excel file
