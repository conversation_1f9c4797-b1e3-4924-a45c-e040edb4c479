// Import Excel data to Convex database
// This script reads the Excel analysis results and imports data to Convex

const fs = require('fs');
const path = require('path');

// Read the Excel analysis results
function loadExcelData() {
  try {
    const analysisFile = './excel-analysis-results.json';
    const data = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));
    return data;
  } catch (error) {
    console.error('Error reading Excel analysis results:', error.message);
    return null;
  }
}

// Transform Active Pursuits data for Convex
function transformActivePursuitsData(sampleData) {
  return sampleData.map(record => ({
    organisation: record.Organisation || null,
    name: record.Name || null,
    email: record.Email || null,
    phone: record.Phone || null,
    mobile: record.Mobile || null,
    address: record.Address || null,
    action_1: record['Action 1'] || null,
    action_2: record['Action 2'] || null,
  })).filter(record => 
    // Only include records with at least organisation or name
    record.organisation || record.name
  );
}

// Transform FOD Centres data for Convex
function transformFODCentresData(sampleData) {
  return sampleData.map(record => ({
    organisation: record.Organisation || null,
    first_name: record['First Name'] || null,
    last_name: record['Last Name'] || null,
    e_mail_address: record['E-mail Address'] || null,
    phone: record.Phone || null,
    mobile_phone: record['Mobile Phone'] || null,
    address_1: record['Address 1'] || null,
    address_2: record['Address 2'] || null,
    postal_code: record['Postal Code'] || null,
    website: record.Website || null,
    emailed: record.Emailed || null,
    signed_up: record['Signed up'] || null,
  })).filter(record => 
    // Only include records with organisation
    record.organisation
  );
}

// Transform Care+Dr data for Convex
function transformCareDrData(sampleData) {
  return sampleData.map(record => ({
    premises: record.premises || null,
    name: record.name || null,
    address: record.address || null,
    left_with: record['left with:'] || null,
    date: record['date:'] || null,
    comments: record.Comments || null,
  })).filter(record => 
    // Only include records with premises or name
    record.premises || record.name
  );
}

// Generate Convex import script
function generateConvexImportScript(excelData) {
  const scripts = [];
  
  // Active Pursuits import
  if (excelData['Active pursuits']) {
    const activePursuitsData = transformActivePursuitsData(excelData['Active pursuits'].sampleData);
    if (activePursuitsData.length > 0) {
      scripts.push(`
// Import Active Pursuits data
import { insertBatchActivePursuits } from "./functions/activepursuits/insertBatch.js";

export const importActivePursuits = mutation({
  args: {},
  handler: async (ctx) => {
    const records = ${JSON.stringify(activePursuitsData, null, 4)};
    
    return await insertBatchActivePursuits(ctx, records);
  },
});`);
    }
  }
  
  // FOD Centres import (if needed as separate table)
  if (excelData['FOD centres']) {
    const fodData = transformFODCentresData(excelData['FOD centres'].sampleData);
    if (fodData.length > 0) {
      scripts.push(`
// Import FOD Centres data (sample)
export const importFODCentresSample = mutation({
  args: {},
  handler: async (ctx) => {
    const records = ${JSON.stringify(fodData, null, 4)};
    
    // Note: This would need a corresponding insertBatch function for fodcentres table
    // Similar to insertBatchActivePursuits
    console.log("FOD Centres data ready for import:", records.length, "records");
    return { success: true, recordCount: records.length };
  },
});`);
    }
  }
  
  return `// Generated Convex import functions
// Run these functions in your Convex dashboard or via API calls

import { mutation } from "./_generated/server";
import { v } from "convex/values";
${scripts.join('\n')}

// Usage Instructions:
// 1. Copy these functions to a new file in your convex/ directory
// 2. Run the import functions from your Convex dashboard
// 3. Check the data was imported correctly using the query functions

// Example API calls:
// POST /api/convex/importActivePursuits
// This will import all the Active Pursuits data from the Excel file
`;
}

// Main execution
async function main() {
  console.log('🔄 Loading Excel analysis data...');
  
  const excelData = loadExcelData();
  if (!excelData) {
    console.error('❌ Failed to load Excel data');
    return;
  }
  
  console.log('📊 Excel data loaded successfully');
  console.log(`Found ${Object.keys(excelData).length} sheets`);
  
  // Generate import script
  const importScript = generateConvexImportScript(excelData);
  
  // Save import script
  const importScriptFile = './convex-import-functions.js';
  fs.writeFileSync(importScriptFile, importScript);
  
  console.log(`\n💾 Convex import script saved to: ${importScriptFile}`);
  
  // Show summary
  console.log('\n📋 Import Summary:');
  Object.entries(excelData).forEach(([sheetName, sheetData]) => {
    if (sheetName !== 'Export Summary' && sheetData.sampleData) {
      console.log(`  - ${sheetName}: ${sheetData.rowCount} total rows, ${sheetData.sampleData.length} sample records`);
    }
  });
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Copy the generated import functions to your Convex project');
  console.log('2. Run the import functions from your Convex dashboard');
  console.log('3. Use the query functions to access the imported data');
  
  console.log('\n✅ Import script generation complete!');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  loadExcelData,
  transformActivePursuitsData,
  transformFODCentresData,
  transformCareDrData,
  generateConvexImportScript
};
