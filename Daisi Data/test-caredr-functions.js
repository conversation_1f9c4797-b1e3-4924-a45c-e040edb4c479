// Test script for Care+Dr Convex functions
// This script validates the function structure and data format

const fs = require('fs');

// Test data sample for Care+Dr
const testRecords = [
  {
    premises: "GP Surgery",
    name: "Test Health Centre",
    address: "123 Medical Drive, Test Town",
    left_with: "Dr. <PERSON>",
    date: 45734, // Excel date number
    comments: "Very enthusiastic about digital inclusion"
  },
  {
    premises: "Library",
    name: "Test Public Library",
    address: "456 Library Street",
    left_with: "Library Manager",
    date: 45735,
    comments: "Interested in hosting sessions"
  }
];

// Validate function structure
function validateCareDrFunctions() {
  console.log('🔍 Validating Care+Dr function files...');
  
  const files = [
    '../Daisi/convex/functions/caredr/insertBatch.js',
    '../Daisi/convex/functions/caredr/queries.js',
    '../Daisi/convex/functions/caredr/mutations.js',
    '../Daisi/convex/functions/caredr/index.js'
  ];
  
  const results = [];
  
  files.forEach(file => {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const hasImports = content.includes('import');
        const hasExports = content.includes('export');
        const hasConvexTypes = content.includes('v.');
        const hasCareDrReferences = content.includes('caredr');
        
        results.push({
          file: file.split('/').pop(),
          exists: true,
          hasImports,
          hasExports,
          hasConvexTypes,
          hasCareDrReferences,
          size: content.length
        });
      } else {
        results.push({
          file: file.split('/').pop(),
          exists: false
        });
      }
    } catch (error) {
      results.push({
        file: file.split('/').pop(),
        exists: false,
        error: error.message
      });
    }
  });
  
  return results;
}

// Validate data format for Care+Dr
function validateCareDrDataFormat(records) {
  console.log('📊 Validating Care+Dr data format...');
  
  const validationResults = {
    totalRecords: records.length,
    validRecords: 0,
    issues: []
  };
  
  records.forEach((record, index) => {
    const recordIssues = [];
    
    // Check required fields
    if (!record.premises && !record.name) {
      recordIssues.push('Missing both premises and name');
    }
    
    // Check date format (should be number for Excel dates)
    if (record.date && typeof record.date !== 'number') {
      recordIssues.push('Date should be a number (Excel date format)');
    }
    
    // Check premises type
    if (record.premises) {
      const validTypes = ['GP Surgery', 'GP surgery', 'Library', 'Care', 'Care Home', 'Hospital', 'Clinic'];
      const isValidType = validTypes.some(type => 
        record.premises.toLowerCase().includes(type.toLowerCase())
      );
      if (!isValidType) {
        recordIssues.push(`Unusual premises type: ${record.premises}`);
      }
    }
    
    if (recordIssues.length === 0) {
      validationResults.validRecords++;
    } else {
      validationResults.issues.push({
        recordIndex: index,
        record: record,
        issues: recordIssues
      });
    }
  });
  
  return validationResults;
}

// Check schema compatibility
function checkSchemaCompatibility() {
  console.log('🔧 Checking schema compatibility...');
  
  try {
    const schemaFile = '../Daisi/convex/schema.ts';
    if (fs.existsSync(schemaFile)) {
      const schemaContent = fs.readFileSync(schemaFile, 'utf8');
      const hasCareDrTable = schemaContent.includes('caredr:');
      const hasProperIndexes = schemaContent.includes('by_premises') && 
                               schemaContent.includes('by_name') && 
                               schemaContent.includes('by_status') &&
                               schemaContent.includes('by_date');
      
      return {
        schemaExists: true,
        hasCareDrTable,
        hasProperIndexes,
        schemaSize: schemaContent.length
      };
    } else {
      return {
        schemaExists: false,
        error: "Schema file not found"
      };
    }
  } catch (error) {
    return {
      schemaExists: false,
      error: error.message
    };
  }
}

// Main test function
function runCareDrTests() {
  console.log('🧪 Testing Care+Dr Convex Functions');
  console.log('='.repeat(50));
  
  // Test 1: Function structure validation
  console.log('\n📁 Function Structure Validation:');
  const structureResults = validateCareDrFunctions();
  console.table(structureResults);
  
  // Test 2: Data format validation
  console.log('\n📊 Data Format Validation:');
  const dataResults = validateCareDrDataFormat(testRecords);
  console.log(`✅ Valid records: ${dataResults.validRecords}/${dataResults.totalRecords}`);
  
  if (dataResults.issues.length > 0) {
    console.log('\n⚠️  Data Issues Found:');
    dataResults.issues.forEach(issue => {
      console.log(`Record ${issue.recordIndex}: ${issue.issues.join(', ')}`);
    });
  }
  
  // Test 3: Schema compatibility
  console.log('\n🔧 Schema Compatibility:');
  const schemaResults = checkSchemaCompatibility();
  console.table([schemaResults]);
  
  console.log('\n🎯 Test Summary:');
  const allFunctionsExist = structureResults.every(r => r.exists);
  const allDataValid = dataResults.validRecords === dataResults.totalRecords;
  const schemaValid = schemaResults.schemaExists && schemaResults.hasCareDrTable;
  
  if (allFunctionsExist && allDataValid && schemaValid) {
    console.log('✅ All tests passed! Care+Dr functions are ready to use.');
  } else {
    console.log('⚠️  Some issues found. Please review the results above.');
  }
  
  console.log('\n📖 Usage Example:');
  console.log(`
// In your Convex dashboard or API:
import { insertBatchCareDr } from "./functions/caredr/insertBatch.js";

// Insert test data:
const result = await insertBatchCareDr(ctx, ${JSON.stringify(testRecords.slice(0, 1), null, 2)});
console.log(result);

// Query data:
import { getAllCareDr, getByPremisesType } from "./functions/caredr/queries.js";
const allRecords = await getAllCareDr(ctx);
const gpSurgeries = await getByPremisesType(ctx, { premisesType: "GP Surgery" });
  `);
}

// Run tests
runCareDrTests();
