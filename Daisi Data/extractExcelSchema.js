// Enhanced Excel Schema Extractor
// Extracts and analyzes Excel data with improved data quality handling

const XLSX = require("xlsx");
const fs = require("fs");

// Path to your Excel file
const filePath = "./Daisi Prospects.xlsx";

// Helper function to clean column names
function cleanColumnName(columnName) {
    if (!columnName || columnName.startsWith('__EMPTY')) {
        return null; // Mark for removal
    }
    return columnName.trim();
}

// Helper function to get data type with better detection
function getDataType(value) {
    if (value === null || value === undefined || value === '') {
        return 'empty';
    }

    const type = typeof value;

    if (type === 'number') {
        // Check if it's a date (Excel dates are numbers)
        if (value > 40000 && value < 50000) { // Rough range for recent dates
            return 'date';
        }
        return Number.isInteger(value) ? 'integer' : 'number';
    }

    if (type === 'string') {
        // Check for email pattern
        if (value.includes('@') && value.includes('.')) {
            return 'email';
        }
        // Check for phone pattern
        if (/^\+?[\d\s\-\(\)]+$/.test(value) && value.length > 6) {
            return 'phone';
        }
        // Check for URL pattern
        if (value.startsWith('http') || value.includes('.com') || value.includes('.co.uk')) {
            return 'url';
        }
        // Check for postal code pattern (UK)
        if (/^[A-Z]{1,2}\d{1,2}\s?\d[A-Z]{2}$/i.test(value)) {
            return 'postcode';
        }
    }

    return type;
}

// Helper function to analyze data quality
function analyzeDataQuality(data) {
    const totalRows = data.length;
    const analysis = {};

    if (totalRows === 0) return analysis;

    // Get all unique column names
    const allColumns = new Set();
    data.forEach(row => {
        Object.keys(row).forEach(key => allColumns.add(key));
    });

    allColumns.forEach(column => {
        const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined && val !== '');
        const nonEmptyCount = values.length;
        const uniqueValues = new Set(values);

        analysis[column] = {
            fillRate: ((nonEmptyCount / totalRows) * 100).toFixed(1) + '%',
            uniqueValues: uniqueValues.size,
            dataTypes: [...new Set(values.map(getDataType))],
            sampleValues: [...uniqueValues].slice(0, 3)
        };
    });

    return analysis;
}

try {
    // Load workbook
    const workbook = XLSX.readFile(filePath);

    // Get all sheet names
    const sheetNames = workbook.SheetNames;

    console.log("📊 Excel File Analysis");
    console.log("=".repeat(50));
    console.log(`File: ${filePath}`);
    console.log(`Sheets found: ${sheetNames.length}`);
    console.log(`Sheet names: ${sheetNames.join(', ')}`);
    console.log();

    const results = {};

    sheetNames.forEach((sheetName, index) => {
        console.log(`\n📋 Sheet ${index + 1}: ${sheetName}`);
        console.log("-".repeat(40));

        const worksheet = workbook.Sheets[sheetName];

        // Convert sheet to JSON (array of objects)
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: null });

        if (jsonData.length === 0) {
            console.log("⚠️  Empty sheet");
            return;
        }

        console.log(`📈 Total rows: ${jsonData.length}`);

        // Clean the data by removing empty columns
        const cleanedData = jsonData.map(row => {
            const cleanedRow = {};
            Object.entries(row).forEach(([key, value]) => {
                const cleanKey = cleanColumnName(key);
                if (cleanKey) {
                    cleanedRow[cleanKey] = value;
                }
            });
            return cleanedRow;
        });

        // Get column information
        const columns = Object.keys(cleanedData[0] || {});
        console.log(`📊 Columns: ${columns.length}`);
        console.log(`Column names: ${columns.join(', ')}`);

        // Analyze data quality
        const qualityAnalysis = analyzeDataQuality(cleanedData);

        console.log("\n📋 Data Quality Analysis:");
        console.table(qualityAnalysis);

        // Preview first 3 rows (cleaned)
        console.log("\n👀 Preview (first 3 rows):");
        console.table(cleanedData.slice(0, 3));

        // Store results
        results[sheetName] = {
            rowCount: cleanedData.length,
            columns: columns,
            qualityAnalysis: qualityAnalysis,
            sampleData: cleanedData.slice(0, 5)
        };
    });

    // Save analysis results to JSON file
    const outputFile = 'excel-analysis-results.json';
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    console.log(`\n💾 Analysis results saved to: ${outputFile}`);

    console.log("\n✅ Analysis complete!");

} catch (error) {
    console.error("❌ Error processing Excel file:", error.message);

    // Provide helpful error messages
    if (error.code === 'ENOENT') {
        console.error(`File not found: ${filePath}`);
        console.error("Please ensure the Excel file exists in the current directory.");
    } else if (error.message.includes('Unsupported file')) {
        console.error("The file format is not supported. Please use .xlsx or .xls files.");
    }
}