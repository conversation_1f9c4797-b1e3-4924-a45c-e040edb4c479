// Generated Convex Schema from Excel file: ./Daisi Prospects.xlsx
// Generated on: 2025-08-10T18:03:29.169Z

import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Generated from Excel sheet: FOD centres
  fodcentres: defineTable({
    // Organisation - Fill rate: 100.0%, Types: string
    organisation: v.string(),
    // First Name - Fill rate: 59.7%, Types: string
    first_name: v.optional(v.string()),
    // Last Name - Fill rate: 55.6%, Types: string
    last_name: v.optional(v.string()),
    // E-mail Address - Fill rate: 88.9%, Types: email
    e_mail_address: v.optional(v.string()),
    // Phone - Fill rate: 55.6%, Types: phone, integer, email
    phone: v.optional(v.string()),
    // Mobile Phone - Fill rate: 31.9%, Types: phone, email, string
    mobile_phone: v.optional(v.string()),
    // Address 1 - Fill rate: 84.7%, Types: string
    address_1: v.optional(v.string()),
    // Address 2 - Fill rate: 72.2%, Types: string
    address_2: v.optional(v.string()),
    // Postal Code - Fill rate: 81.9%, Types: postcode, string
    postal_code: v.optional(v.string()),
    // Website - Fill rate: 69.4%, Types: url, string
    website: v.optional(v.string()),
    // Emailed - Fill rate: 0.0%, Types: 
    emailed: v.optional(v.string()),
    // Signed up - Fill rate: 0.0%, Types: 
    signed_up: v.optional(v.string()),
  }),

  // Generated from Excel sheet: Care+Dr
  caredr: defineTable({
    // premises - Fill rate: 75.0%, Types: string
    premises: v.optional(v.string()),
    // name - Fill rate: 75.0%, Types: string
    name: v.optional(v.string()),
    // address - Fill rate: 75.0%, Types: string
    address: v.optional(v.string()),
    // left with: - Fill rate: 73.0%, Types: string
    left_with: v.optional(v.string()),
    // date: - Fill rate: 71.0%, Types: date
    date: v.optional(v.number()),
    // Comments - Fill rate: 27.0%, Types: string, email
    comments: v.optional(v.string()),
  }),

  // Generated from Excel sheet: Active pursuits
  activepursuits: defineTable({
    // Organisation - Fill rate: 35.7%, Types: string
    organisation: v.optional(v.string()),
    // Name - Fill rate: 64.3%, Types: string
    name: v.optional(v.string()),
    // Email - Fill rate: 42.9%, Types: email
    email: v.optional(v.string()),
    // Phone - Fill rate: 25.0%, Types: phone, email
    phone: v.optional(v.string()),
    // Mobile - Fill rate: 7.1%, Types: phone
    mobile: v.optional(v.string()),
    // Address - Fill rate: 67.9%, Types: string, postcode
    address: v.optional(v.string()),
    // Action 1 - Fill rate: 28.6%, Types: string
    action_1: v.optional(v.string()),
    // Action 2 - Fill rate: 28.6%, Types: string
    action_2: v.optional(v.string()),
  }),
});

// Usage Instructions:
// 1. Copy this schema to your Convex project's convex/schema.ts file
// 2. Merge with existing schema if needed
// 3. Run 'npx convex dev' to apply schema changes
// 4. Use the generated table names in your Convex functions

// Table Names Generated:
// - fodcentres
// - caredr
// - activepursuits
