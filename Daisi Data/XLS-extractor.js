// save as extractExcelSchema.js

const XLSX = require("xlsx");
const fs = require("fs");

// Path to your Excel file
const filePath = "./Daisi Prospects.xlsx";

// Load workbook
const workbook = XLSX.readFile(filePath);

// Get all sheet names
const sheetNames = workbook.SheetNames;

console.log("Sheets found:", sheetNames);

sheetNames.forEach((sheetName) => {
  const worksheet = workbook.Sheets[sheetName];
  
  // Convert sheet to JSON (array of objects)
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: null });
  
  // Preview first 5 rows
  console.log(`\nSheet: ${sheetName}`);
  console.log("Preview of first 5 rows:");
  console.table(jsonData.slice(0, 5));

  // Determine column data types from first 10 rows
  const columnTypes = {};

  jsonData.slice(0, 10).forEach((row) => {
    Object.entries(row).forEach(([key, value]) => {
      if (value === null) return;
      const currentType = columnTypes[key];
      const valueType = typeof value;

      if (!currentType) {
        columnTypes[key] = valueType;
      } else if (currentType !== valueType) {
        // If mixed types found, mark as 'mixed'
        columnTypes[key] = "mixed";
      }
    });
  });

  console.log("Detected column data types:");
  console.table(columnTypes);
});
